﻿namespace Centerpoint.Common.Constants
{
    public static class ServiceImprovementStatusConstant
    {

        public const string Draft = "DFT";
        public const string PendingApproval = "PEA";
        public const string Accepted = "ACC";
        public const string CorrectiveActionRequested = "CAR";
        public const string CorrectiveActionSubmitted = "CAS";
        public const string CorrectiveActionReEvaluated = "CRE";
        public const string CorrectiveActionAccepted = "CAC";
        public const string InvestigationRequested = "INR";
        public const string InvestigationSubmitted = "INS";
        public const string InvestigationReRequested = "IRR";
        public const string InvestigationAccepted = "INA";
        public const string PreventiveActionRequested = "RAR";
        public const string PreventiveActionSubmitted = "RAS";
        public const string PreventiveActionReEvaluated = "RRE";
        public const string PreventiveActionAccepted = "RAC";
        public const string Closed = "CLS";
        public const string Rejected = "REJ";
        public const string Abandoned = "ABD";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Draft,"Draft"},
                    {PendingApproval,"Pending Approval"},
                    {Accepted,"Accepted"},
                    {CorrectiveActionRequested,"Corrective Action Requested"},
                    {CorrectiveActionSubmitted,"Corrective Action Submitted"},
                    {CorrectiveActionReEvaluated,"Corrective Action Re-Evaluated"},
                    {CorrectiveActionAccepted,"Corrective Action Accepted"},
                    {InvestigationRequested,"Investigation Requested"},
                    {InvestigationSubmitted,"Investigation Submitted"},
                    {InvestigationReRequested,"Investigation Re-Requested"},
                    {InvestigationAccepted,"Investigation Accepted"},
                    {PreventiveActionRequested,"Preventive Action Requested"},
                    {PreventiveActionSubmitted,"Preventive Action Submitted"},
                    {PreventiveActionReEvaluated,"Preventive Action Re-Evaluated"},
                    {PreventiveActionAccepted,"Preventive Action Accepted"},
                    {Closed,"Closed"},
                    {Rejected,"Rejected"},
                    {Abandoned,"Abandoned"},
                };
            }
        }


        public static bool IsInvestigationStatus(string status)
        {
            return InvestigationStatuses.Contains(status);
        }

        public static string[] InvestigationStatuses
        {
            get
            {
                return new string[] {
                    CorrectiveActionAccepted,
                    InvestigationRequested,
                    InvestigationSubmitted,
                    InvestigationReRequested,
                    InvestigationAccepted,
                    PreventiveActionRequested,
                    PreventiveActionSubmitted,
                    PreventiveActionReEvaluated,
                    PreventiveActionAccepted,
                    Closed

                };
            }

        }

        public static bool IsPreventiveActionStatus(string status)
        {
            return PreventiveActionStatuses.Contains(status);
        }

        public static string[] PreventiveActionStatuses
        {
            get
            {
                return new string[] {
                    InvestigationAccepted,
                    PreventiveActionRequested,
                    PreventiveActionSubmitted,
                    PreventiveActionReEvaluated,
                    PreventiveActionAccepted,
                    Closed
                };
            }

        }

        public static bool IsCorrectiveActionStatus(string status)
        {
            return CorrectiveActionStatuses.Contains(status);
        }

        public static string[] CorrectiveActionStatuses
        {
            get
            {
                return new string[] {
                    InvestigationAccepted,
                    CorrectiveActionAccepted,
                    CorrectiveActionReEvaluated,
                    CorrectiveActionRequested,
                    CorrectiveActionSubmitted,             
                    Closed
                };
            }

        }

        public static bool IsPreventiveImplementationStatus(string status)
        {
            return PreventiveImplementationStatuses.Contains(status);
        }

        public static string[] PreventiveImplementationStatuses
        {
            get
            {
                return new string[] {
                    PreventiveActionAccepted,
                    Closed
                };
            }

        }

        public static bool IsUserNameStatuses(string status)
        {
            return UserNameStatuses.Contains(status);
        }

        public static string[] UserNameStatuses
        {
            get
            {
                return new string[] {
                    InvestigationSubmitted,
                    PreventiveActionSubmitted,
                    CorrectiveActionSubmitted,
                    Closed,
                    InvestigationAccepted,
                    PreventiveActionAccepted,
                    CorrectiveActionAccepted
                };
            }

        }
    }
}
